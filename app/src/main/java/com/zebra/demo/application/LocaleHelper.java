package com.zebra.demo.application;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;

import com.zebra.demo.R;

import java.util.Locale;

/**
 * 语言管理工具类
 * 用于处理应用的语言切换和本地化
 */
public class LocaleHelper {
    private static final String TAG = "LocaleHelper";
    private static final String LANGUAGE_KEY = "language_key";
    private static final String LANGUAGE_EN = "en";
    private static final String LANGUAGE_ZH = "zh";

    /**
     * 设置应用语言
     * 
     * @param context  上下文
     * @param language 语言代码 ("en" 或 "zh")
     * @return 更新后的上下文
     */
    public static Context setLocale(Context context, String language) {
        persistLanguage(context, language);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return updateResources(context, language);
        }

        return updateResourcesLegacy(context, language);
    }

    /**
     * 获取当前语言
     *
     * @param context 上下文
     * @return 当前语言代码
     */
    public static String getLanguage(Context context) {
        SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
        String savedLanguage = prefs.getString(LANGUAGE_KEY, null);

        // 如果没有保存的语言设置，检查系统语言
        if (savedLanguage == null) {
            String systemLanguage = getSystemLanguage();
            Log.d(TAG, "No saved language, system language: " + systemLanguage);
            // 如果系统语言是中文，则使用中文，否则使用英文
            savedLanguage = systemLanguage.startsWith("zh") ? LANGUAGE_ZH : LANGUAGE_EN;
            // 保存检测到的语言
            persistLanguage(context, savedLanguage);
        }

        Log.d(TAG, "Current language: " + savedLanguage);
        return savedLanguage;
    }

    /**
     * 获取当前语言索引
     * 
     * @param context 上下文
     * @return 语言索引 (0: 英文, 1: 中文)
     */
    public static int getLanguageIndex(Context context) {
        String language = getLanguage(context);
        return LANGUAGE_ZH.equals(language) ? 1 : 0;
    }

    /**
     * 根据索引设置语言
     * 
     * @param context 上下文
     * @param index   语言索引 (0: 英文, 1: 中文)
     * @return 更新后的上下文
     */
    public static Context setLanguageByIndex(Context context, int index) {
        String language = (index == 1) ? LANGUAGE_ZH : LANGUAGE_EN;
        return setLocale(context, language);
    }

    /**
     * 持久化语言设置
     * 
     * @param context  上下文
     * @param language 语言代码
     */
    private static void persistLanguage(Context context, String language) {
        SharedPreferences prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE);
        prefs.edit().putString(LANGUAGE_KEY, language).apply();
        Log.d(TAG, "Language saved: " + language);
    }

    /**
     * 更新资源 (API 24+)
     * 
     * @param context  上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    private static Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Configuration configuration = new Configuration(context.getResources().getConfiguration());
        configuration.setLocale(locale);

        return context.createConfigurationContext(configuration);
    }

    /**
     * 更新资源 (API 23 及以下)
     * 
     * @param context  上下文
     * @param language 语言代码
     * @return 更新后的上下文
     */
    private static Context updateResourcesLegacy(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Resources resources = context.getResources();
        Configuration configuration = new Configuration(resources.getConfiguration());
        configuration.locale = locale;
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());

        return context;
    }

    /**
     * 检查是否需要重启应用
     * 
     * @param context          上下文
     * @param newLanguageIndex 新的语言索引
     * @return 是否需要重启
     */
    public static boolean needsRestart(Context context, int newLanguageIndex) {
        int currentIndex = getLanguageIndex(context);
        return currentIndex != newLanguageIndex;
    }

    /**
     * 获取语言显示名称
     * 
     * @param context 上下文
     * @param index   语言索引
     * @return 语言显示名称
     */
    public static String getLanguageDisplayName(Context context, int index) {
        String[] languages = context.getResources().getStringArray(R.array.language_options);
        if (index >= 0 && index < languages.length) {
            return languages[index];
        }
        return languages[0]; // 默认返回英文
    }
}