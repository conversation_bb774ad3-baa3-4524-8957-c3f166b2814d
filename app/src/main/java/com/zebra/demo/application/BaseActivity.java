package com.zebra.demo.application;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;

/**
 * 基础Activity类
 * 支持语言切换功能
 */
public abstract class BaseActivity extends AppCompatActivity {

    @Override
    protected void attachBaseContext(Context newBase) {
        // 在Activity创建时应用语言设置
        Context context = LocaleHelper.setLocale(newBase, LocaleHelper.getLanguage(newBase));
        super.attachBaseContext(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // 处理配置变化
        LocaleHelper.setLocale(this, LocaleHelper.getLanguage(this));
    }
}